-- 图片搜索数据库表结构设计
-- 数据库: image_search_db

-- 1. 图片基本信息表
CREATE TABLE IF NOT EXISTS `image_entity` (
    `id` VARCHAR(64) NOT NULL COMMENT '图片唯一标识符',
    `url` VARCHAR(512) NOT NULL COMMENT '图片访问URL地址',
    `original_filename` VARCHAR(255) COMMENT '原始文件名',
    `file_path` VARCHAR(512) COMMENT '文件存储路径',
    `thumbnail_path` VARCHAR(512) COMMENT '缩略图存储路径',
    `semantic_score` FLOAT DEFAULT 0.0 COMMENT '语义相似度得分',
    `metadata_score` FLOAT DEFAULT 0.0 COMMENT '元数据匹配得分',
    `combined_score` FLOAT DEFAULT 0.0 COMMENT '综合得分',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-删除',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_url` (`url`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_time` (`created_time`),
    INDEX `idx_semantic_score` (`semantic_score`),
    INDEX `idx_combined_score` (`combined_score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片基本信息表';

-- 2. 图片元数据表
CREATE TABLE IF NOT EXISTS `image_metadata` (
    `id` BIGINT AUTO_INCREMENT COMMENT '主键ID',
    `image_id` VARCHAR(64) NOT NULL COMMENT '图片ID，关联image_entity.id',
    `width` INT NOT NULL DEFAULT 0 COMMENT '图片宽度（像素）',
    `height` INT NOT NULL DEFAULT 0 COMMENT '图片高度（像素）',
    `file_size` BIGINT NOT NULL DEFAULT 0 COMMENT '文件大小（字节）',
    `format` VARCHAR(20) NOT NULL COMMENT '图片格式（jpg、png、gif等）',
    `dominant_color` VARCHAR(7) COMMENT '主色调（十六进制颜色值）',
    `upload_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    `exif_data` JSON COMMENT 'EXIF数据（JSON格式）',
    `color_histogram` JSON COMMENT '颜色直方图数据',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_image_id` (`image_id`),
    INDEX `idx_width` (`width`),
    INDEX `idx_height` (`height`),
    INDEX `idx_file_size` (`file_size`),
    INDEX `idx_format` (`format`),
    INDEX `idx_upload_time` (`upload_time`),
    CONSTRAINT `fk_metadata_image_id` FOREIGN KEY (`image_id`) REFERENCES `image_entity` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片元数据表';

-- 3. 图片标签表
CREATE TABLE IF NOT EXISTS `image_tag` (
    `id` BIGINT AUTO_INCREMENT COMMENT '主键ID',
    `image_id` VARCHAR(64) NOT NULL COMMENT '图片ID，关联image_entity.id',
    `tag_name` VARCHAR(100) NOT NULL COMMENT '标签名称',
    `tag_type` VARCHAR(50) DEFAULT 'manual' COMMENT '标签类型：manual-手动，auto-自动识别',
    `confidence` FLOAT DEFAULT 1.0 COMMENT '置信度（0.0-1.0）',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    INDEX `idx_image_id` (`image_id`),
    INDEX `idx_tag_name` (`tag_name`),
    INDEX `idx_tag_type` (`tag_type`),
    INDEX `idx_confidence` (`confidence`),
    CONSTRAINT `fk_tag_image_id` FOREIGN KEY (`image_id`) REFERENCES `image_entity` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片标签表';

-- 4. 图片特征向量表
CREATE TABLE IF NOT EXISTS `image_feature_vector` (
    `id` BIGINT AUTO_INCREMENT COMMENT '主键ID',
    `image_id` VARCHAR(64) NOT NULL COMMENT '图片ID，关联image_entity.id',
    `vector_type` VARCHAR(50) NOT NULL DEFAULT 'clip' COMMENT '向量类型：clip、resnet、vit等',
    `vector_dimension` INT NOT NULL COMMENT '向量维度',
    `feature_vector` BLOB COMMENT '特征向量（二进制存储）',
    `vector_norm` FLOAT COMMENT '向量模长',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_image_vector_type` (`image_id`, `vector_type`),
    INDEX `idx_vector_type` (`vector_type`),
    INDEX `idx_vector_dimension` (`vector_dimension`),
    CONSTRAINT `fk_vector_image_id` FOREIGN KEY (`image_id`) REFERENCES `image_entity` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片特征向量表';

-- 5. 图片搜索历史表（可选）
CREATE TABLE IF NOT EXISTS `image_search_history` (
    `id` BIGINT AUTO_INCREMENT COMMENT '主键ID',
    `search_query` TEXT COMMENT '搜索查询文本',
    `search_strategy` VARCHAR(50) COMMENT '搜索策略',
    `filter_conditions` JSON COMMENT '筛选条件（JSON格式）',
    `result_count` INT DEFAULT 0 COMMENT '结果数量',
    `search_time_ms` BIGINT DEFAULT 0 COMMENT '搜索耗时（毫秒）',
    `user_id` VARCHAR(64) COMMENT '用户ID（可选）',
    `ip_address` VARCHAR(45) COMMENT 'IP地址',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    INDEX `idx_search_query` (`search_query`(100)),
    INDEX `idx_search_strategy` (`search_strategy`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片搜索历史表';
