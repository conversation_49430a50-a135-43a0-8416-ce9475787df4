# 图片搜索服务配置
image-search:
  # 默认搜索配置
  defaultConfig:
    limit: 10
    timeout: 5000
  # 语义搜索配置
  semantic:
    enabled: true
    model: "text-embedding-ada-002"
    similarity-threshold: 0.7
  # 筛选搜索配置
  filter:
    enabled: true
    max-filters: 10
  # 图片详情配置
  detail:
    include-exif: true
    include-similar: true
    similar-limit: 5

# MinIO 对象存储配置
minio:
  endpoint: ${MINIO_ENDPOINT:http://8.141.11.126:9000}
  access-key: ${MINIO_ACCESS_KEY:minioadmin}
  secret-key: ${MINIO_SECRET_KEY:minioadmin123}
  bucket-name: ${MINIO_BUCKET:image-storage}
  # 图片存储路径前缀
  image-path-prefix: images/
  # 缩略图路径前缀
  thumbnail-path-prefix: thumbnails/
  # 上传配置
  upload:
    max-file-size: 10MB
    allowed-formats: jpg,jpeg,png,gif,webp,svg
  # 访问配置
  public-read: true

spring:
  application:
    name: mcp-server-image-search

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
      enabled: true

  # 数据库配置
  datasource:
    url: ******************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    driver-class-name: org.mariadb.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000


  # MyBatis配置
  mybatis:
    mapper-locations: classpath:mapper/*.xml
    type-aliases-package: cn.bugstack.mcp.server.csdn.infrastructure.po
    configuration:
      map-underscore-to-camel-case: true
      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

  ai:
    mcp:
      server:
        name: mcp-server-image-search
        type: SYNC
        version: 1.0.0
        instructions: "图片搜索MCP服务器，提供语义搜索、条件筛选和详情获取功能"
        sse-message-endpoint: /mcp/messages
        capabilities:
          tool: true
          resource: true
          prompt: true
          completion: true

  main:
    banner-mode: off
# stdio 模式打开，sse 模式，注释掉。
#    web-application-type: none

logging:
# stdio 模式打开，sse 模式，注释掉。
#  pattern:
#    console:
  file:
    name: data/log/${spring.application.name}.log

server:
  port: 8099
  servlet:
    encoding:
      charset: UTF-8
      force: true
      enabled: true
