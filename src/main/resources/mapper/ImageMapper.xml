<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iflytek.imagesearch.infrastructure.persistence.mapper.ImageMapper">

    <!-- 结果映射 -->
    <resultMap id="ImagePOResultMap" type="cn.iflytek.imagesearch.infrastructure.persistence.po.ImagePO">
        <id column="id" property="id"/>
        <result column="url" property="url"/>
        <result column="width" property="width"/>
        <result column="height" property="height"/>
        <result column="file_size" property="fileSize"/>
        <result column="format" property="format"/>
        <result column="upload_time" property="uploadTime"/>
        <result column="dominant_color" property="dominantColor"/>
        <result column="feature_vector" property="featureVector"/>
        <result column="semantic_score" property="semanticScore"/>
        <result column="metadata_score" property="metadataScore"/>
        <result column="combined_score" property="combinedScore"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, url, width, height, file_size, format, upload_time, dominant_color,
        feature_vector, semantic_score, metadata_score, combined_score, created_time, updated_time
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" resultMap="ImagePOResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM image
        WHERE id = #{imageId}
    </select>

    <!-- 根据URL查询 -->
    <select id="selectByUrl" resultMap="ImagePOResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM image
        WHERE url = #{imageUrl}
    </select>

    <!-- 根据ID列表批量查询 -->
    <select id="selectByIds" resultMap="ImagePOResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM image
        WHERE id IN
        <foreach collection="imageIds" item="imageId" open="(" separator="," close=")">
            #{imageId}
        </foreach>
    </select>

    <!-- 插入记录 -->
    <insert id="insert">
        INSERT INTO image (
            id, url, width, height, file_size, format, upload_time, dominant_color,
            feature_vector, semantic_score, metadata_score, combined_score
        ) VALUES (
            #{id}, #{url}, #{width}, #{height}, #{fileSize}, #{format}, #{uploadTime}, #{dominantColor},
            #{featureVector}, #{semanticScore}, #{metadataScore}, #{combinedScore}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="insertBatch">
        INSERT INTO image (
            id, url, width, height, file_size, format, upload_time, dominant_color,
            feature_vector, semantic_score, metadata_score, combined_score
        ) VALUES
        <foreach collection="imageList" item="image" separator=",">
            (#{image.id}, #{image.url}, #{image.width}, #{image.height}, #{image.fileSize}, 
             #{image.format}, #{image.uploadTime}, #{image.dominantColor}, #{image.featureVector}, 
             #{image.semanticScore}, #{image.metadataScore}, #{image.combinedScore})
        </foreach>
    </insert>

    <!-- 更新记录 -->
    <update id="update">
        UPDATE image
        <set>
            <if test="url != null">url = #{url},</if>
            <if test="width != null">width = #{width},</if>
            <if test="height != null">height = #{height},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="format != null">format = #{format},</if>
            <if test="uploadTime != null">upload_time = #{uploadTime},</if>
            <if test="dominantColor != null">dominant_color = #{dominantColor},</if>
            <if test="featureVector != null">feature_vector = #{featureVector},</if>
            <if test="semanticScore != null">semantic_score = #{semanticScore},</if>
            <if test="metadataScore != null">metadata_score = #{metadataScore},</if>
            <if test="combinedScore != null">combined_score = #{combinedScore},</if>
            updated_time = CURRENT_TIMESTAMP
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteById">
        DELETE FROM image WHERE id = #{imageId}
    </delete>

    <!-- 检查是否存在 -->
    <select id="existsById" resultType="int">
        SELECT COUNT(1) FROM image WHERE id = #{imageId}
    </select>

    <!-- 获取总数 -->
    <select id="count" resultType="long">
        SELECT COUNT(1) FROM image
    </select>

    <!-- 分页查询 -->
    <select id="selectAll" resultMap="ImagePOResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM image
        ORDER BY created_time DESC
        LIMIT #{offset}, #{limit}
    </select>

</mapper>
