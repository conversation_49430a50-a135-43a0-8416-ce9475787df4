package cn.iflytek.imagesearch.infrastructure.persistence.mapper;

import cn.iflytek.imagesearch.infrastructure.persistence.po.ImagePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 图片数据访问Mapper接口
 */
@Mapper
public interface ImageMapper {

    /**
     * 根据图片ID查找图片
     *
     * @param imageId 图片ID
     * @return 图片PO对象
     */
    ImagePO selectById(@Param("imageId") String imageId);

    /**
     * 根据图片URL查找图片
     *
     * @param imageUrl 图片URL
     * @return 图片PO对象
     */
    ImagePO selectByUrl(@Param("imageUrl") String imageUrl);

    /**
     * 根据图片ID列表批量查找图片
     *
     * @param imageIds 图片ID列表
     * @return 图片PO对象列表
     */
    List<ImagePO> selectByIds(@Param("imageIds") List<String> imageIds);

    /**
     * 插入图片记录
     *
     * @param imagePO 图片PO对象
     * @return 影响行数
     */
    int insert(ImagePO imagePO);

    /**
     * 批量插入图片记录
     *
     * @param imageList 图片PO对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("imageList") List<ImagePO> imageList);

    /**
     * 更新图片记录
     *
     * @param imagePO 图片PO对象
     * @return 影响行数
     */
    int update(ImagePO imagePO);

    /**
     * 根据图片ID删除图片记录
     *
     * @param imageId 图片ID
     * @return 影响行数
     */
    int deleteById(@Param("imageId") String imageId);

    /**
     * 检查图片是否存在
     *
     * @param imageId 图片ID
     * @return 存在返回1，不存在返回0
     */
    int existsById(@Param("imageId") String imageId);

    /**
     * 获取图片总数
     *
     * @return 图片总数
     */
    long count();

    /**
     * 分页查询图片列表
     *
     * @param offset 偏移量
     * @param limit  限制数量
     * @return 图片PO对象列表
     */
    List<ImagePO> selectAll(@Param("offset") int offset, @Param("limit") int limit);
}
