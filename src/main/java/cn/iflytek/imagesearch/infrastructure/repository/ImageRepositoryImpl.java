package cn.iflytek.imagesearch.infrastructure.repository;

import cn.iflytek.imagesearch.domain.model.entry.ImageEntity;
import cn.iflytek.imagesearch.domain.repository.ImageRepository;
import cn.iflytek.imagesearch.infrastructure.persistence.converter.ImageConverter;
import cn.iflytek.imagesearch.infrastructure.persistence.mapper.ImageMapper;
import cn.iflytek.imagesearch.infrastructure.persistence.mapper.ImageTagMapper;
import cn.iflytek.imagesearch.infrastructure.persistence.po.ImagePO;
import cn.iflytek.imagesearch.infrastructure.persistence.po.ImageTagPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 图片仓储接口实现类
 */
@Repository
public class ImageRepositoryImpl implements ImageRepository {

    @Autowired
    private ImageMapper imageMapper;

    @Autowired
    private ImageTagMapper imageTagMapper;

    @Override
    public Optional<ImageEntity> findById(String imageId) {
        if (imageId == null || imageId.trim().isEmpty()) {
            return Optional.empty();
        }

        ImagePO imagePO = imageMapper.selectById(imageId);
        if (imagePO == null) {
            return Optional.empty();
        }

        List<ImageTagPO> tagPOList = imageTagMapper.selectByImageId(imageId);
        ImageEntity imageEntity = ImageConverter.toImageEntity(imagePO, tagPOList);
        return Optional.ofNullable(imageEntity);
    }

    @Override
    public Optional<ImageEntity> findByUrl(String imageUrl) {
        if (imageUrl == null || imageUrl.trim().isEmpty()) {
            return Optional.empty();
        }

        ImagePO imagePO = imageMapper.selectByUrl(imageUrl);
        if (imagePO == null) {
            return Optional.empty();
        }

        List<ImageTagPO> tagPOList = imageTagMapper.selectByImageId(imagePO.getId());
        ImageEntity imageEntity = ImageConverter.toImageEntity(imagePO, tagPOList);
        return Optional.ofNullable(imageEntity);
    }

    @Override
    public List<ImageEntity> findByIds(List<String> imageIds) {
        if (imageIds == null || imageIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 过滤空值
        List<String> validIds = imageIds.stream()
                .filter(id -> id != null && !id.trim().isEmpty())
                .collect(Collectors.toList());

        if (validIds.isEmpty()) {
            return new ArrayList<>();
        }

        List<ImagePO> imagePOList = imageMapper.selectByIds(validIds);
        if (imagePOList.isEmpty()) {
            return new ArrayList<>();
        }

        // 批量查询标签
        List<ImageTagPO> allTagPOList = imageTagMapper.selectByImageIds(validIds);
        Map<String, List<ImageTagPO>> tagMap = allTagPOList.stream()
                .collect(Collectors.groupingBy(ImageTagPO::getImageId));

        return ImageConverter.toImageEntityList(imagePOList, tagMap);
    }

    @Override
    @Transactional
    public ImageEntity save(ImageEntity imageEntity) {
        if (imageEntity == null || imageEntity.getId() == null) {
            throw new IllegalArgumentException("ImageEntity or imageId cannot be null");
        }

        // 转换为PO对象
        ImagePO imagePO = ImageConverter.toImagePO(imageEntity);

        // 检查是否已存在
        boolean exists = imageMapper.existsById(imageEntity.getId()) > 0;

        if (exists) {
            // 更新图片记录
            imageMapper.update(imagePO);
        } else {
            // 插入图片记录
            imageMapper.insert(imagePO);
        }

        // 处理标签
        saveImageTags(imageEntity);

        return imageEntity;
    }

    @Override
    @Transactional
    public List<ImageEntity> saveAll(List<ImageEntity> imageEntities) {
        if (imageEntities == null || imageEntities.isEmpty()) {
            return new ArrayList<>();
        }

        List<ImageEntity> savedEntities = new ArrayList<>();
        for (ImageEntity entity : imageEntities) {
            if (entity != null && entity.getId() != null) {
                savedEntities.add(save(entity));
            }
        }

        return savedEntities;
    }

    @Override
    @Transactional
    public boolean deleteById(String imageId) {
        if (imageId == null || imageId.trim().isEmpty()) {
            return false;
        }

        // 先删除标签（由于外键约束，会自动删除）
        imageTagMapper.deleteByImageId(imageId);

        // 删除图片记录
        int deletedRows = imageMapper.deleteById(imageId);
        return deletedRows > 0;
    }

    @Override
    public boolean existsById(String imageId) {
        if (imageId == null || imageId.trim().isEmpty()) {
            return false;
        }
        return imageMapper.existsById(imageId) > 0;
    }

    @Override
    public long count() {
        return imageMapper.count();
    }

    @Override
    public List<ImageEntity> findAll(int offset, int limit) {
        if (offset < 0 || limit <= 0) {
            return new ArrayList<>();
        }

        List<ImagePO> imagePOList = imageMapper.selectAll(offset, limit);
        if (imagePOList.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取所有图片ID
        List<String> imageIds = imagePOList.stream()
                .map(ImagePO::getId)
                .collect(Collectors.toList());

        // 批量查询标签
        List<ImageTagPO> allTagPOList = imageTagMapper.selectByImageIds(imageIds);
        Map<String, List<ImageTagPO>> tagMap = allTagPOList.stream()
                .collect(Collectors.groupingBy(ImageTagPO::getImageId));

        return ImageConverter.toImageEntityList(imagePOList, tagMap);
    }

    /**
     * 保存图片标签
     *
     * @param imageEntity 图片实体
     */
    private void saveImageTags(ImageEntity imageEntity) {
        String imageId = imageEntity.getId();

        // 先删除原有标签
        imageTagMapper.deleteByImageId(imageId);

        // 插入新标签
        if (imageEntity.getMetadata() != null && imageEntity.getMetadata().getTags() != null) {
            List<String> tags = imageEntity.getMetadata().getTags();
            if (!tags.isEmpty()) {
                List<ImageTagPO> tagPOList = ImageConverter.toImageTagPOList(imageId, tags);
                imageTagMapper.insertBatch(tagPOList);
            }
        }
    }
}
