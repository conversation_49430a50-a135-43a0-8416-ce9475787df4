package cn.iflytek.imagesearch.domain.service.search.impl;


import cn.iflytek.imagesearch.domain.model.entry.ImageEntity;
import cn.iflytek.imagesearch.domain.model.entry.ImageMetadata;
import cn.iflytek.imagesearch.domain.model.entry.ImageMetadataFilter;
import cn.iflytek.imagesearch.domain.model.request.ImageDetailRequest;
import cn.iflytek.imagesearch.domain.model.request.SearchRequest;
import cn.iflytek.imagesearch.domain.model.response.ImageDetailResponse;
import cn.iflytek.imagesearch.domain.model.response.SearchResult;
import cn.iflytek.imagesearch.domain.repository.ImageRepository;
import cn.iflytek.imagesearch.domain.service.search.IImageSearchService;
import cn.iflytek.imagesearch.domain.service.search.MetadataSearchService;
import cn.iflytek.imagesearch.domain.service.search.VectorSearchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 图片搜索服务实现
 * 注意：这里只是框架实现，具体的搜索逻辑需要根据实际的图片数据源进行实现
 */
@Service
public class ImageSearchServiceImpl implements IImageSearchService {

    private final Logger log = LoggerFactory.getLogger(ImageSearchServiceImpl.class);

    @Autowired
    private VectorSearchService vectorSearchService;

    @Autowired
    private MetadataSearchService metadataSearchService;

    @Autowired
    private ImageRepository imageRepository;


    @Override
    public SearchResult semanticSearch(SearchRequest request) {
        log.info("开始执行图片搜索，策略: {}, 查询: {}, 限制数量: {}",
                request.getStrategy(), request.getQuery(), request.getLimit());

        long startTime = System.currentTimeMillis();

        try {
            SearchResult result;

            // 根据搜索策略选择不同的搜索方法
            switch (request.getStrategy()) {
                case SEMANTIC_ONLY:
                    // 纯语义搜索：仅使用向量搜索，不考虑元数据筛选
                    result = semanticSearchOnly(request);
                    break;
                case METADATA_ONLY:
                    // 纯元数据搜索：仅使用元数据筛选，不进行语义匹配
                    result = metadataSearchOnly(request);
                    break;
                case SEMANTIC_FIRST:
                    // 语义优先：先进行语义搜索获取候选集，再应用元数据筛选
                    result = semanticFirstSearch(request);
                    break;
                case METADATA_FIRST:
                    // 元数据优先：先进行元数据筛选获取候选集，再进行语义搜索
                    result = metadataFirstSearch(request);
                    break;
                case HYBRID:
                default:
                    // 混合搜索：并行执行语义搜索和元数据筛选，然后融合评分
                    result = hybridSearchWithScoring(request);
                    break;
            }

            // 设置搜索耗时
            long searchTime = System.currentTimeMillis() - startTime;
            result.setSearchTime(searchTime);

            log.info("搜索完成，返回结果数: {}, 耗时: {}ms", result.getResults().size(), searchTime);
            return result;

        } catch (Exception e) {
            log.error("图片搜索执行失败", e);
            throw new RuntimeException("图片搜索执行失败", e);
        }
    }




    /**
     * 混合搜索 - 评分融合策略
     */
    private SearchResult<ImageEntity> hybridSearchWithScoring(SearchRequest request) {
        List<ImageEntity> candidates = new ArrayList<>();

        // 1. 并行执行语义搜索和元数据筛选
        CompletableFuture<List<ImageEntity>> semanticFuture =
                CompletableFuture.supplyAsync(() ->
                        vectorSearchService.semanticSearch(request.getQuery(), request.getLimit() * 3));

        CompletableFuture<Set<String>> metadataFuture =
                CompletableFuture.supplyAsync(() ->
                        metadataSearchService.filterByMetadata(request.getMetadata()));

        try {
            List<ImageEntity> semanticResults = semanticFuture.get();
            Set<String> metadataFilteredIds = metadataFuture.get();

            // 2. 计算综合得分
            for (ImageEntity image : semanticResults) {
                float semanticScore = image.getSemanticScore();
                float metadataScore = calculateMetadataScore(image, request.getMetadata());

                // 如果有元数据筛选条件，且不满足，则跳过
                if (!request.getMetadata().isEmpty() &&
                        !metadataFilteredIds.contains(image.getId())) {
                    continue;
                }

                // 综合评分：加权平均
                float combinedScore = 0.7f * semanticScore + 0.3f * metadataScore;
                image.setCombinedScore(combinedScore);
                image.setMetadataScore(metadataScore);

                candidates.add(image);
            }

            // 3. 按综合得分排序
            candidates.sort((a, b) -> Float.compare(b.getCombinedScore(), a.getCombinedScore()));

            // 4. 限制返回结果数量
            List<ImageEntity> finalResults = candidates.stream()
                    .limit(request.getLimit())
                    .collect(Collectors.toList());

            return new SearchResult<ImageEntity>(finalResults, candidates.size(), 1L);

        } catch (Exception e) {
            log.error("混合搜索执行失败", e);
            throw new RuntimeException("混合搜索执行失败", e);
        }
    }

    /**
     * 纯语义搜索策略
     * 仅使用向量搜索进行语义匹配，不考虑元数据筛选条件
     * 适用于注重语义相关性的场景
     *
     * @param request 搜索请求
     * @return 搜索结果
     */
    private SearchResult<ImageEntity> semanticSearchOnly(SearchRequest request) {
        log.debug("执行纯语义搜索，查询: {}", request.getQuery());

        // 直接调用向量搜索服务进行语义搜索
        List<ImageEntity> semanticResults =
                vectorSearchService.semanticSearch(request.getQuery(), request.getLimit());

        log.debug("语义搜索返回 {} 个结果", semanticResults.size());

        return new SearchResult<>(semanticResults, semanticResults.size(), 0L);
    }

    /**
     * 纯元数据搜索策略
     * 仅使用元数据筛选条件进行搜索，不进行语义匹配
     * 适用于精确筛选的场景，如按尺寸、格式、时间等条件筛选
     *
     * @param request 搜索请求
     * @return 搜索结果
     */
    private SearchResult<ImageEntity> metadataSearchOnly(SearchRequest request) {
        log.debug("执行纯元数据搜索，筛选条件: {}", request.getMetadata());

        // 检查是否有元数据筛选条件
        if (request.getMetadata() == null || request.getMetadata().isEmpty()) {
            log.warn("元数据搜索请求中没有筛选条件，返回空结果");
            return new SearchResult<>(new ArrayList<>(), 0L, 0L);
        }

        // 使用元数据搜索服务进行筛选
        List<ImageEntity> metadataResults =
                metadataSearchService.buildSpecification(request.getMetadata());

        // 限制返回结果数量
        List<ImageEntity> limitedResults = metadataResults.stream()
                .limit(request.getLimit())
                .collect(Collectors.toList());

        log.debug("元数据搜索返回 {} 个结果", limitedResults.size());

        return new SearchResult<>(limitedResults, metadataResults.size(), 0L);
    }

    /**
     * 语义优先策略：先语义搜索，再元数据筛选
     */
    private SearchResult<ImageEntity> semanticFirstSearch(SearchRequest request) {
        // 1. 语义搜索获取候选集
        List<ImageEntity> semanticResults =
                vectorSearchService.semanticSearch(request.getQuery(), request.getLimit() * 2);

        // 2. 应用元数据筛选
        List<ImageEntity> filteredResults = semanticResults.stream()
                .filter(image -> matchesMetadataFilter(image, request.getMetadata()))
                .limit(request.getLimit())
                .collect(Collectors.toList());

        return new SearchResult<>(filteredResults, filteredResults.size(), 0L);
    }

    /**
     * 元数据优先策略：先元数据筛选，再语义搜索
     * 先通过元数据条件筛选出候选集，然后在候选集内进行语义搜索
     * 适用于有明确筛选条件且希望在筛选结果中找到最相关内容的场景
     *
     * @param request 搜索请求
     * @return 搜索结果
     */
    private SearchResult<ImageEntity> metadataFirstSearch(SearchRequest request) {
        log.debug("执行元数据优先搜索");

        // 1. 元数据筛选获取候选集
        Set<String> metadataFilteredIds =
                metadataSearchService.filterByMetadata(request.getMetadata());

        log.debug("元数据筛选得到 {} 个候选图片", metadataFilteredIds.size());

        // 2. 在候选集内进行语义搜索
        List<ImageEntity> finalResults =
                vectorSearchService.semanticSearchWithinCandidates(
                        request.getQuery(), metadataFilteredIds, request.getLimit());

        log.debug("在候选集内语义搜索返回 {} 个结果", finalResults.size());

        return new SearchResult<>(finalResults, finalResults.size(), 0L);
    }

    /**
     * 计算图片的元数据匹配得分
     * 根据图片的元数据信息与筛选条件的匹配程度计算得分
     * 得分范围：0.0 - 1.0，1.0表示完全匹配
     *
     * @param image 图片实体
     * @param filter 元数据筛选条件
     * @return 元数据匹配得分
     */
    private float calculateMetadataScore(ImageEntity image, ImageMetadataFilter filter) {
        if (filter == null || filter.isEmpty() || image.getMetadata() == null) {
            return 0.0f;
        }

        ImageMetadata metadata = image.getMetadata();
        float totalScore = 0.0f;
        int criteriaCount = 0;

        // 1. 尺寸匹配得分
        if (filter.getSizeRange() != null) {
            criteriaCount++;
            if (isSizeInRange(metadata, filter.getSizeRange())) {
                totalScore += 1.0f;
            }
        }

        // 2. 文件大小匹配得分
        if (filter.getMinFileSize() != null || filter.getMaxFileSize() != null) {
            criteriaCount++;
            if (isFileSizeInRange(metadata, filter.getMinFileSize(), filter.getMaxFileSize())) {
                totalScore += 1.0f;
            }
        }

        // 3. 格式匹配得分
        if (filter.getFormats() != null && !filter.getFormats().isEmpty()) {
            criteriaCount++;
            if (filter.getFormats().contains(metadata.getFormat().toLowerCase())) {
                totalScore += 1.0f;
            }
        }

        // 4. 时间范围匹配得分
        if (filter.getDateRange() != null) {
            criteriaCount++;
            if (isDateInRange(metadata, filter.getDateRange())) {
                totalScore += 1.0f;
            }
        }

        // 5. 标签匹配得分（部分匹配也给分）
        if (filter.getTags() != null && !filter.getTags().isEmpty()) {
            criteriaCount++;
            float tagScore = calculateTagMatchScore(metadata.getTags(), filter.getTags());
            totalScore += tagScore;
        }

        // 6. 宽高比匹配得分
        if (filter.getAspectRatio() != null) {
            criteriaCount++;
            if (isAspectRatioInRange(metadata, filter.getAspectRatio())) {
                totalScore += 1.0f;
            }
        }

        // 返回平均得分
        return criteriaCount > 0 ? totalScore / criteriaCount : 0.0f;
    }

    /**
     * 检查图片是否匹配元数据筛选条件
     * 用于语义优先搜索中的元数据筛选
     *
     * @param image 图片实体
     * @param filter 元数据筛选条件
     * @return 是否匹配筛选条件
     */
    private boolean matchesMetadataFilter(ImageEntity image, ImageMetadataFilter filter) {
        if (filter == null || filter.isEmpty() || image.getMetadata() == null) {
            return true; // 没有筛选条件时认为匹配
        }

        ImageMetadata metadata = image.getMetadata();

        // 检查尺寸范围
        if (filter.getSizeRange() != null && !isSizeInRange(metadata, filter.getSizeRange())) {
            return false;
        }

        // 检查文件大小范围
        if (!isFileSizeInRange(metadata, filter.getMinFileSize(), filter.getMaxFileSize())) {
            return false;
        }

        // 检查格式
        if (filter.getFormats() != null && !filter.getFormats().isEmpty()) {
            if (!filter.getFormats().contains(metadata.getFormat().toLowerCase())) {
                return false;
            }
        }

        // 检查时间范围
        if (filter.getDateRange() != null && !isDateInRange(metadata, filter.getDateRange())) {
            return false;
        }

        // 检查标签（必须包含所有指定标签）
        if (filter.getTags() != null && !filter.getTags().isEmpty()) {
            if (metadata.getTags() == null || !metadata.getTags().containsAll(filter.getTags())) {
                return false;
            }
        }

        // 检查宽高比范围
        if (filter.getAspectRatio() != null && !isAspectRatioInRange(metadata, filter.getAspectRatio())) {
            return false;
        }

        return true;
    }

    /**
     * 检查图片尺寸是否在指定范围内
     */
    private boolean isSizeInRange(ImageMetadata metadata, ImageMetadataFilter.SizeRange sizeRange) {
        if (sizeRange.getMinWidth() != null && metadata.getWidth() < sizeRange.getMinWidth()) {
            return false;
        }
        if (sizeRange.getMaxWidth() != null && metadata.getWidth() > sizeRange.getMaxWidth()) {
            return false;
        }
        if (sizeRange.getMinHeight() != null && metadata.getHeight() < sizeRange.getMinHeight()) {
            return false;
        }
        if (sizeRange.getMaxHeight() != null && metadata.getHeight() > sizeRange.getMaxHeight()) {
            return false;
        }
        return true;
    }

    /**
     * 检查文件大小是否在指定范围内
     */
    private boolean isFileSizeInRange(ImageMetadata metadata, Long minSize, Long maxSize) {
        if (minSize != null && metadata.getFileSize() < minSize) {
            return false;
        }
        if (maxSize != null && metadata.getFileSize() > maxSize) {
            return false;
        }
        return true;
    }

    /**
     * 检查上传时间是否在指定范围内
     */
    private boolean isDateInRange(ImageMetadata metadata, ImageMetadataFilter.DateRange dateRange) {
        LocalDateTime uploadTime = metadata.getUploadTime();
        if (uploadTime == null) {
            return false;
        }

        if (dateRange.getStartDate() != null && uploadTime.isBefore(dateRange.getStartDate())) {
            return false;
        }
        if (dateRange.getEndDate() != null && uploadTime.isAfter(dateRange.getEndDate())) {
            return false;
        }
        return true;
    }

    /**
     * 检查宽高比是否在指定范围内
     */
    private boolean isAspectRatioInRange(ImageMetadata metadata, ImageMetadataFilter.AspectRatioRange aspectRatio) {
        if (metadata.getHeight() == 0) {
            return false;
        }

        double ratio = (double) metadata.getWidth() / metadata.getHeight();

        if (aspectRatio.getMinRatio() != null && ratio < aspectRatio.getMinRatio()) {
            return false;
        }
        if (aspectRatio.getMaxRatio() != null && ratio > aspectRatio.getMaxRatio()) {
            return false;
        }
        return true;
    }

    /**
     * 计算标签匹配得分
     * 返回匹配标签数量与要求标签数量的比例
     */
    private float calculateTagMatchScore(List<String> imageTags, List<String> requiredTags) {
        if (imageTags == null || imageTags.isEmpty()) {
            return 0.0f;
        }

        long matchCount = requiredTags.stream()
                .mapToLong(tag -> imageTags.contains(tag) ? 1 : 0)
                .sum();

        return (float) matchCount / requiredTags.size();
    }



    @Override
    public ImageDetailResponse getImageDetail(ImageDetailRequest request) {
        log.info("获取图片详情，图片ID: {}, URL: {}, 包含扩展信息: {}, 包含相似图片: {}",
                request.getImageId(), request.getImageUrl(),
                request.getIncludeExtendedInfo(), request.getIncludeSimilarImages());

        try {
            ImageDetailResponse response = new ImageDetailResponse();
            ImageEntity imageEntity = null;

            // 1. 根据ID或URL获取图片实体
            if (request.getImageId() != null && !request.getImageId().trim().isEmpty()) {
                // 优先使用图片ID查询
                Optional<ImageEntity> optionalImage = imageRepository.findById(request.getImageId());
                if (optionalImage.isPresent()) {
                    imageEntity = optionalImage.get();
                    log.debug("通过ID找到图片: {}", request.getImageId());
                } else {
                    log.warn("未找到ID为 {} 的图片", request.getImageId());
                }
            } else if (request.getImageUrl() != null && !request.getImageUrl().trim().isEmpty()) {
                // 使用图片URL查询
                Optional<ImageEntity> optionalImage = imageRepository.findByUrl(request.getImageUrl());
                if (optionalImage.isPresent()) {
                    imageEntity = optionalImage.get();
                    log.debug("通过URL找到图片: {}", request.getImageUrl());
                } else {
                    log.warn("未找到URL为 {} 的图片", request.getImageUrl());
                }
            }

            if (imageEntity == null) {
                // 图片不存在
                response.setSuccess(false);
                response.setErrorMessage("图片不存在");
                response.setImageEntity(null);
                return response;
            }

            // 2. 如果需要包含相似图片推荐
            if (request.getIncludeSimilarImages() != null && request.getIncludeSimilarImages()) {
                List<ImageEntity> similarImages = findSimilarImages(imageEntity, request.getSimilarImagesLimit());
                // 注意：ImageDetailResponse类中没有similarImages字段，这里只是演示逻辑
                // 实际使用时需要扩展ImageDetailResponse类或在imageEntity中包含相似图片信息
                log.debug("找到 {} 个相似图片", similarImages.size());
            }

            // 3. 如果需要包含扩展信息（这里可以添加EXIF数据、颜色分析等）
            if (request.getIncludeExtendedInfo() != null && request.getIncludeExtendedInfo()) {
                // TODO: 可以在这里添加EXIF数据提取、颜色分析等扩展功能
                // 可以将扩展信息添加到imageEntity的metadata中
                log.debug("包含扩展信息处理（待实现具体逻辑）");
            }

            response.setSuccess(true);
            response.setImageEntity(imageEntity);
            response.setErrorMessage(null); // 成功时清空错误信息

            log.info("成功获取图片详情，图片ID: {}", imageEntity.getId());
            return response;

        } catch (Exception e) {
            log.error("获取图片详情失败", e);

            ImageDetailResponse response = new ImageDetailResponse();
            response.setSuccess(false);
            response.setErrorMessage("获取图片详情失败: " + e.getMessage());
            response.setImageEntity(null);

            return response;
        }
    }

    /**
     * 查找相似图片
     * 基于图片的特征向量进行相似度搜索
     *
     * @param targetImage 目标图片
     * @param limit 返回数量限制
     * @return 相似图片列表
     */
    private List<ImageEntity> findSimilarImages(ImageEntity targetImage, Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 5; // 默认返回5个相似图片
        }

        try {
            // 如果目标图片有特征向量，可以进行向量相似度搜索
            if (targetImage.getFeatureVector() != null) {
                // TODO: 这里可以实现基于特征向量的相似图片搜索
                // 例如使用向量数据库进行KNN搜索
                log.debug("基于特征向量搜索相似图片（待实现具体逻辑）");
            }

            // 临时实现：返回空列表，实际应用中需要根据具体的向量搜索服务实现
            return new ArrayList<>();

        } catch (Exception e) {
            log.error("查找相似图片失败", e);
            return new ArrayList<>();
        }
    }



}
