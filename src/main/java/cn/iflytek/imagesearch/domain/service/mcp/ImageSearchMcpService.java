package cn.iflytek.imagesearch.domain.service.mcp;


import cn.iflytek.imagesearch.domain.model.request.ImageDetailRequest;
import cn.iflytek.imagesearch.domain.model.request.SearchRequest;
import cn.iflytek.imagesearch.domain.model.response.ImageDetailResponse;
import cn.iflytek.imagesearch.domain.model.response.SearchResult;
import cn.iflytek.imagesearch.domain.service.search.IImageSearchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Component;

/**
 * 图片搜索MCP服务
 * 提供图片搜索相关的MCP工具
 */
@Component
public class ImageSearchMcpService {

    private final Logger log = LoggerFactory.getLogger(ImageSearchMcpService.class);

    private final IImageSearchService imageSearchService;

    public ImageSearchMcpService(IImageSearchService imageSearchService) {
        this.imageSearchService = imageSearchService;
    }

    /**
     * 语义搜索图片工具
     * 基于自然语言描述进行智能图片搜索
     */
    @Tool(description = "基于自然语言描述进行智能图片搜索。支持复杂的语义理解，能够根据描述内容找到相关图片。")
    public SearchResult semanticSearchImages(SearchRequest request) {
        log.info("执行语义搜索图片，请求参数: {}", request);

        try {
            SearchResult response = imageSearchService.semanticSearch(request);
            return response;
        } catch (Exception e) {
            log.error("语义搜索图片失败", e);
            SearchResult errorResponse = new SearchResult();
            return errorResponse;
        }
    }



    /**
     * 获取图片详情工具
     * 获取单张图片的完整元数据信息
     */
    @Tool(description = "获取单张图片的完整元数据信息。包括基本信息、EXIF数据、颜色分析、相似图片推荐等详细信息。")
    public ImageDetailResponse getImageDetail(ImageDetailRequest request) {
        log.info("获取图片详情，请求参数: {}", request);

        try {
            ImageDetailResponse response = imageSearchService.getImageDetail(request);
            log.info("获取图片详情完成，图片ID: {}", request.getImageId());
            return response;
        } catch (Exception e) {
            log.error("获取图片详情失败", e);
            ImageDetailResponse errorResponse = new ImageDetailResponse();
            errorResponse.setSuccess(false);
            errorResponse.setErrorMessage("获取图片详情失败: " + e.getMessage());
            return errorResponse;
        }
    }


}
