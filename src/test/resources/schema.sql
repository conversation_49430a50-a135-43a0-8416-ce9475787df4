-- 测试环境数据库表结构（H2数据库）
-- 创建图片主表
CREATE TABLE IF NOT EXISTS `image` (
    `id` VARCHAR(64) NOT NULL COMMENT '图片唯一标识符',
    `url` VARCHAR(512) NOT NULL COMMENT '图片访问URL地址',
    `width` INT NOT NULL DEFAULT 0 COMMENT '图片宽度，单位像素',
    `height` INT NOT NULL DEFAULT 0 COMMENT '图片高度，单位像素',
    `file_size` BIGINT NOT NULL DEFAULT 0 COMMENT '文件大小，单位字节',
    `format` VARCHAR(10) NOT NULL DEFAULT '' COMMENT '图片格式，如jpg、png、gif等',
    `upload_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '图片上传时间',
    `dominant_color` VARCHAR(7) DEFAULT NULL COMMENT '图片主色调，十六进制颜色值，如#FF0000',
    `feature_vector` CLOB DEFAULT NULL COMMENT '图片特征向量，JSON格式存储',
    `semantic_score` FLOAT DEFAULT 0.0 COMMENT '语义相似度得分，范围0.0-1.0',
    `metadata_score` FLOAT DEFAULT 0.0 COMMENT '元数据匹配得分，范围0.0-1.0',
    `combined_score` FLOAT DEFAULT 0.0 COMMENT '综合得分，结合语义和元数据得分的加权平均值',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录更新时间',
    PRIMARY KEY (`id`)
);

-- 创建图片标签表
CREATE TABLE IF NOT EXISTS `image_tag` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `image_id` VARCHAR(64) NOT NULL COMMENT '图片ID，关联image表',
    `tag` VARCHAR(100) NOT NULL COMMENT '标签名称',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    PRIMARY KEY (`id`),
    FOREIGN KEY (`image_id`) REFERENCES `image` (`id`) ON DELETE CASCADE
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_image_width ON image(width);
CREATE INDEX IF NOT EXISTS idx_image_height ON image(height);
CREATE INDEX IF NOT EXISTS idx_image_file_size ON image(file_size);
CREATE INDEX IF NOT EXISTS idx_image_format ON image(format);
CREATE INDEX IF NOT EXISTS idx_image_upload_time ON image(upload_time);
CREATE INDEX IF NOT EXISTS idx_image_tag_tag ON image_tag(tag);
CREATE INDEX IF NOT EXISTS idx_image_tag_image_id ON image_tag(image_id);
